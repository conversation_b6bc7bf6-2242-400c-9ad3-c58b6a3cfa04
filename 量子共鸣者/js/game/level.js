/**
 * 量子共鸣者 - 关卡系统
 * 管理关卡配置、目标、进度和完成条件
 */

class Level {
    constructor(config) {
        this.config = config || {};
        this.name = config.name || '未命名关卡';
        this.description = config.description || '';
        
        // 关卡目标
        this.objectives = config.objectives || [];
        this.targetScore = config.targetScore || 1000;
        this.timeLimit = config.timeLimit || 60; // 秒
        this.maxMoves = config.maxMoves || null; // 最大移动次数
        
        // 关卡状态
        this.isLoaded = false;
        this.isCompleted = false;
        this.isFailed = false;
        this.startTime = 0;
        this.elapsedTime = 0;
        this.moveCount = 0;
        
        // 粒子配置
        this.particleConfigs = config.particles || [];
        this.forceFieldConfigs = config.forceFields || [];
        
        // 特殊规则
        this.rules = config.rules || {};
        this.powerUps = config.powerUps || [];
        
        // 评分系统
        this.scoreMultipliers = config.scoreMultipliers || {
            time: 1.0,
            moves: 1.0,
            combo: 1.0
        };
        
        console.log(`📋 关卡已创建: ${this.name}`);
    }

    /**
     * 加载关卡
     */
    async load() {
        try {
            console.log(`📋 加载关卡: ${this.name}`);

            // 清空现有的游戏对象（检查引擎是否存在）
            if (window.physicsEngine && typeof physicsEngine.clear === 'function') {
                physicsEngine.clear();
            }
            if (window.quantumEngine && typeof quantumEngine.reset === 'function') {
                quantumEngine.reset();
            }

            // 创建粒子
            this.createParticles();

            // 创建力场
            this.createForceFields();

            // 应用特殊规则
            this.applyRules();

            // 设置关卡状态
            this.isLoaded = true;
            this.startTime = Date.now();
            this.elapsedTime = 0;
            this.moveCount = 0;

            // 更新UI
            this.updateLevelUI();

            console.log(`✅ 关卡加载完成: ${this.name}`);

        } catch (error) {
            console.error('❌ 关卡加载失败:', error);
            throw error;
        }
    }

    /**
     * 创建粒子
     */
    createParticles() {
        // 检查物理引擎是否可用
        if (!window.physicsEngine || typeof physicsEngine.createParticle !== 'function') {
            console.warn('⚠️ 物理引擎不可用，跳过粒子创建');
            return;
        }

        this.particleConfigs.forEach((config, index) => {
            try {
                const particle = physicsEngine.createParticle({
                    x: config.x || 100 + index * 100,
                    y: config.y || 100 + index * 50,
                    z: config.z || 0,
                    radius: config.radius || 10,
                    mass: config.mass || 1,
                    frequency: config.frequency || 440 + index * 110,
                    energy: config.energy || 1,
                    color: config.color || this.getParticleColor(config.frequency),
                    type: config.type || 'quantum',
                    isStatic: config.isStatic || false,
                    userData: {
                        levelId: this.name,
                        originalIndex: index,
                        ...config.userData
                    }
                });

                // 设置特殊属性
                if (config.isTarget) {
                    particle.userData.isTarget = true;
                }

                if (config.isObstacle) {
                    particle.userData.isObstacle = true;
                    particle.color = '#ff6b6b';
                }
            } catch (error) {
                console.error(`❌ 创建粒子 ${index} 失败:`, error);
            }
        });
    }

    /**
     * 创建力场
     */
    createForceFields() {
        // 检查物理引擎是否可用
        if (!window.physicsEngine || typeof physicsEngine.createForceField !== 'function') {
            console.warn('⚠️ 物理引擎不可用，跳过力场创建');
            return;
        }

        this.forceFieldConfigs.forEach((config, index) => {
            try {
                physicsEngine.createForceField({
                    x: config.x || 0,
                    y: config.y || 0,
                    z: config.z || 0,
                    type: config.type || 'radial',
                    strength: config.strength || 100,
                    radius: config.radius || 100,
                    direction: config.direction || { x: 0, y: -1, z: 0 },
                    isActive: config.isActive !== undefined ? config.isActive : true,
                    affectsTypes: config.affectsTypes || ['quantum']
                });
            } catch (error) {
                console.error(`❌ 创建力场 ${index} 失败:`, error);
            }
        });
    }

    /**
     * 应用特殊规则
     */
    applyRules() {
        // 重力设置（检查物理引擎是否存在）
        if (this.rules.gravity && window.physicsEngine) {
            physicsEngine.gravity = this.rules.gravity;
        }

        // 阻尼设置（检查物理引擎是否存在）
        if (this.rules.damping && window.physicsEngine) {
            physicsEngine.damping = this.rules.damping;
        }

        // 量子场强度（检查量子引擎是否存在）
        if (this.rules.fieldStrength && window.quantumEngine) {
            quantumEngine.fieldStrength = this.rules.fieldStrength;
        }

        // 共鸣阈值（检查量子引擎是否存在）
        if (this.rules.resonanceThreshold && window.quantumEngine) {
            quantumEngine.resonanceThreshold = this.rules.resonanceThreshold;
        }

        // 频率范围限制（检查量子引擎是否存在）
        if (this.rules.frequencyRange && window.quantumEngine) {
            quantumEngine.minFrequency = this.rules.frequencyRange.min;
            quantumEngine.maxFrequency = this.rules.frequencyRange.max;
        }
    }

    /**
     * 更新关卡状态
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        if (!this.isLoaded || this.isCompleted || this.isFailed) return;
        
        // 更新时间
        this.elapsedTime = (Date.now() - this.startTime) / 1000;
        
        // 检查时间限制
        if (this.timeLimit > 0 && this.elapsedTime >= this.timeLimit) {
            this.fail('时间用尽');
            return;
        }
        
        // 检查移动次数限制
        if (this.maxMoves && this.moveCount >= this.maxMoves) {
            this.fail('移动次数用尽');
            return;
        }
        
        // 检查完成条件
        this.checkCompletionConditions();
        
        // 更新UI
        this.updateLevelUI();
    }

    /**
     * 检查完成条件
     */
    checkCompletionConditions() {
        // 检查分数目标（检查量子引擎是否存在）
        if (this.targetScore > 0 && window.quantumEngine && quantumEngine.score >= this.targetScore) {
            this.complete('达到目标分数');
            return;
        }

        // 检查自定义目标
        for (const objective of this.objectives) {
            if (this.checkObjective(objective)) {
                this.complete(`完成目标: ${objective.description}`);
                return;
            }
        }

        // 检查所有目标粒子是否激活（检查物理引擎是否存在）
        if (window.physicsEngine && physicsEngine.particles) {
            const targetParticles = physicsEngine.particles.filter(p =>
                p.userData && p.userData.isTarget
            );

            if (targetParticles.length > 0) {
                const allActivated = targetParticles.every(p => p.isActive);
                if (allActivated) {
                    this.complete('所有目标粒子已激活');
                return;
            }
        }
    }

    /**
     * 检查单个目标
     * @param {Object} objective - 目标对象
     * @returns {boolean} 是否完成
     */
    checkObjective(objective) {
        switch (objective.type) {
            case 'score':
                return window.quantumEngine && quantumEngine.score >= objective.target;

            case 'combo':
                return window.quantumEngine && quantumEngine.combo >= objective.target;

            case 'activateParticles':
                if (!window.physicsEngine || !physicsEngine.particles) return false;
                const activeCount = physicsEngine.particles.filter(p => p.isActive).length;
                return activeCount >= objective.target;

            case 'chainReaction':
                return window.quantumEngine && quantumEngine.resonanceChains &&
                       quantumEngine.resonanceChains.some(chain =>
                           chain.length >= objective.target
                       );

            case 'frequency':
                return window.quantumEngine &&
                       Math.abs(quantumEngine.targetFrequency - objective.target) <= objective.tolerance;

            case 'time':
                return this.elapsedTime <= objective.target;

            default:
                return false;
        }
    }

    /**
     * 完成关卡
     * @param {string} reason - 完成原因
     */
    complete(reason = '') {
        if (this.isCompleted) return;
        
        this.isCompleted = true;
        console.log(`🎉 关卡完成: ${this.name} - ${reason}`);
        
        // 计算奖励分数
        const bonusScore = this.calculateBonusScore();
        if (window.quantumEngine) {
            quantumEngine.score += bonusScore;
        }
        
        // 保存关卡记录
        this.saveRecord();
        
        // 触发完成事件
        this.onComplete(reason, bonusScore);
    }

    /**
     * 关卡失败
     * @param {string} reason - 失败原因
     */
    fail(reason = '') {
        if (this.isFailed) return;
        
        this.isFailed = true;
        console.log(`💥 关卡失败: ${this.name} - ${reason}`);
        
        // 触发失败事件
        this.onFail(reason);
    }

    /**
     * 计算奖励分数
     * @returns {number} 奖励分数
     */
    calculateBonusScore() {
        let bonus = 0;
        
        // 时间奖励
        if (this.timeLimit > 0) {
            const timeRatio = Math.max(0, 1 - this.elapsedTime / this.timeLimit);
            bonus += Math.floor(timeRatio * 500 * this.scoreMultipliers.time);
        }
        
        // 移动次数奖励
        if (this.maxMoves) {
            const moveRatio = Math.max(0, 1 - this.moveCount / this.maxMoves);
            bonus += Math.floor(moveRatio * 300 * this.scoreMultipliers.moves);
        }
        
        // 连击奖励（检查量子引擎是否存在）
        if (window.quantumEngine && typeof quantumEngine.combo !== 'undefined') {
            bonus += quantumEngine.combo * 10 * this.scoreMultipliers.combo;
        }
        
        // 完美完成奖励
        if (this.isPerfectCompletion()) {
            bonus += 1000;
        }
        
        return bonus;
    }

    /**
     * 检查是否完美完成
     * @returns {boolean} 是否完美完成
     */
    isPerfectCompletion() {
        // 在时间限制的一半内完成
        const timeBonus = this.timeLimit > 0 && this.elapsedTime <= this.timeLimit * 0.5;
        
        // 移动次数少于限制的一半
        const moveBonus = !this.maxMoves || this.moveCount <= this.maxMoves * 0.5;
        
        // 达到高分（检查量子引擎是否存在）
        const scoreBonus = window.quantumEngine && quantumEngine.score >= this.targetScore * 1.5;
        
        return timeBonus && moveBonus && scoreBonus;
    }

    /**
     * 保存关卡记录
     */
    async saveRecord() {
        try {
            const record = {
                levelName: this.name,
                score: window.quantumEngine ? quantumEngine.score : 0,
                time: this.elapsedTime,
                moves: this.moveCount,
                combo: window.quantumEngine ? quantumEngine.combo : 0,
                completed: this.isCompleted,
                perfect: this.isPerfectCompletion(),
                timestamp: Date.now()
            };
            
            // 保存到本地存储
            const records = await storageService.get('level.records') || [];
            records.push(record);
            
            // 只保留最近100条记录
            if (records.length > 100) {
                records.splice(0, records.length - 100);
            }
            
            await storageService.put('level.records', records);
            
            // 更新最佳记录
            const bestKey = `level.best.${this.name}`;
            const bestRecord = await storageService.get(bestKey);
            
            if (!bestRecord || record.score > bestRecord.score) {
                await storageService.put(bestKey, record);
            }
            
        } catch (error) {
            console.error('❌ 关卡记录保存失败:', error);
        }
    }

    /**
     * 获取粒子颜色
     * @param {number} frequency - 频率
     * @returns {string} 颜色值
     */
    getParticleColor(frequency) {
        // 根据频率映射颜色
        const hue = ((frequency - 20) / (20000 - 20)) * 360;
        return `hsl(${hue}, 70%, 60%)`;
    }

    /**
     * 更新关卡UI
     */
    updateLevelUI() {
        // 更新时间显示
        const timeElement = document.getElementById('timeValue');
        if (timeElement) {
            const remaining = Math.max(0, this.timeLimit - this.elapsedTime);
            timeElement.textContent = this.formatTime(remaining);
        }

        // 更新目标显示
        const targetElement = document.getElementById('targetScore');
        if (targetElement) {
            targetElement.textContent = this.targetScore;
        }

        // 更新进度条（检查量子引擎是否存在）
        const progressElement = document.getElementById('progressBar');
        if (progressElement) {
            let progress = 0;
            if (window.quantumEngine && typeof quantumEngine.score !== 'undefined') {
                progress = Math.min(100, (quantumEngine.score / this.targetScore) * 100);
            }
            progressElement.style.width = `${progress}%`;
        }

        // 更新移动次数
        if (this.maxMoves) {
            const movesElement = document.getElementById('movesValue');
            if (movesElement) {
                movesElement.textContent = `${this.moveCount}/${this.maxMoves}`;
            }
        }
    }

    /**
     * 格式化时间显示
     * @param {number} seconds - 秒数
     * @returns {string} 格式化的时间字符串
     */
    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    /**
     * 重置关卡
     */
    reset() {
        this.isCompleted = false;
        this.isFailed = false;
        this.startTime = Date.now();
        this.elapsedTime = 0;
        this.moveCount = 0;
        
        // 重新加载关卡
        this.load();
    }

    /**
     * 增加移动次数
     */
    incrementMoveCount() {
        this.moveCount++;
    }

    /**
     * 获取关卡统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            name: this.name,
            description: this.description,
            isLoaded: this.isLoaded,
            isCompleted: this.isCompleted,
            isFailed: this.isFailed,
            elapsedTime: this.elapsedTime,
            timeLimit: this.timeLimit,
            moveCount: this.moveCount,
            maxMoves: this.maxMoves,
            targetScore: this.targetScore,
            currentScore: window.quantumEngine ? quantumEngine.score : 0,
            progress: this.targetScore > 0 && window.quantumEngine ? (quantumEngine.score / this.targetScore) : 0,
            particleCount: window.physicsEngine && physicsEngine.particles ? physicsEngine.particles.length : 0,
            activeParticles: window.physicsEngine && physicsEngine.particles ? physicsEngine.particles.filter(p => p.isActive).length : 0
        };
    }

    /**
     * 完成事件回调
     * @param {string} reason - 完成原因
     * @param {number} bonusScore - 奖励分数
     */
    onComplete(reason, bonusScore) {
        // 可以被外部重写
        console.log(`🎉 关卡完成回调: ${reason}, 奖励: ${bonusScore}`);
    }

    /**
     * 失败事件回调
     * @param {string} reason - 失败原因
     */
    onFail(reason) {
        // 可以被外部重写
        console.log(`💥 关卡失败回调: ${reason}`);
    }

    /**
     * 检查关卡是否完成
     * @returns {boolean} 是否完成
     */
    isComplete() {
        return this.isCompleted;
    }

    /**
     * 检查关卡是否失败
     * @returns {boolean} 是否失败
     */
    hasFailed() {
        return this.isFailed;
    }
}

/**
 * 关卡注册器 - 负责关卡的注册和创建
 * 注意：这个类与 level-manager.js 中的 LevelManager 不同
 * 这个类专门用于关卡的注册和创建，而 LevelManager 负责完整的关卡管理功能
 */
class LevelRegistry {
    constructor() {
        this.levels = new Map();
        this.currentLevel = null;
        this.levelPacks = new Map();
    }

    /**
     * 注册关卡
     * @param {string} id - 关卡ID
     * @param {Object} config - 关卡配置
     */
    registerLevel(id, config) {
        this.levels.set(id, config);
    }

    /**
     * 创建关卡
     * @param {string} id - 关卡ID
     * @returns {Level} 关卡对象
     */
    createLevel(id) {
        const config = this.levels.get(id);
        if (!config) {
            throw new Error(`关卡不存在: ${id}`);
        }

        return new Level(config);
    }

    /**
     * 获取所有关卡ID
     * @returns {Array} 关卡ID数组
     */
    getAllLevelIds() {
        return Array.from(this.levels.keys());
    }

    /**
     * 注册关卡包
     * @param {string} packId - 关卡包ID
     * @param {Object} packConfig - 关卡包配置
     */
    registerLevelPack(packId, packConfig) {
        this.levelPacks.set(packId, packConfig);

        // 注册包中的所有关卡
        packConfig.levels.forEach((levelConfig, index) => {
            const levelId = `${packId}.${index}`;
            this.registerLevel(levelId, levelConfig);
        });
    }
}

// 导出类到全局作用域
window.Level = Level;
window.LevelRegistry = LevelRegistry;

// 创建全局关卡注册器实例
window.levelRegistry = new LevelRegistry();
